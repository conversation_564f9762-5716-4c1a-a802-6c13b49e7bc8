package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.filtros.FiltroSolicitacaoCompraJSON;
import com.pacto.adm.core.dto.solicitacaocompra.SolicitacaoCompraDTO;
import com.pacto.adm.core.services.interfaces.SolicitacaoCompraService;
import com.pacto.adm.core.swagger.respostas.solicitacaocompra.ExemploRespostaListSolicitacaoCompraPaginacao;
import com.pacto.adm.core.swagger.respostas.solicitacaocompra.ExemploRespostaSolicitacaoCompra;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/solicitacao-compra")
public class SolicitacaoCompraController {

    @Autowired
    private SolicitacaoCompraService solicitacaoCompraService;

    @GetMapping
    public ResponseEntity<EnvelopeRespostaDTO> listarSolicitacoes(
            @RequestParam(required = false) String filters,
            PaginadorDTO paginadorDTO
    ) {
        try {
            JSONObject filtros = filters != null ? new JSONObject(filters) : null;

            FiltroSolicitacaoCompraJSON filtroSolicitacaoCompraJSON = new FiltroSolicitacaoCompraJSON(filtros);
            return ResponseEntityFactory.ok(solicitacaoCompraService.listarSolicitacoes(filtroSolicitacaoCompraJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(@RequestBody SolicitacaoCompraDTO solicitacaoCompraDTO) {
        try {
            return ResponseEntityFactory.ok(solicitacaoCompraService.saveOrUpdate(solicitacaoCompraDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e + "/n");
            StackTraceElement[] stackTrace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : stackTrace) {
                result.append(stackTraceElement.toString() + "/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(solicitacaoCompraService.buscarSolicitacao(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
